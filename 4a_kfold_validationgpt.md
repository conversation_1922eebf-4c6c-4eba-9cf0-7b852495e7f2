# Simplest Hyperparameter Tuning for **Well-Log Curve Regression**

**Short answer:**
No—it's **not necessary** to do *RandomizedSearchCV followed by GridSearchCV*. For most well-log regressions, a **single `RandomizedSearchCV`** with a modest number of trials (e.g., 30–60) and **GroupKFold by well** gives excellent results at a fraction of the compute. If time is extremely tight, run a **tiny GridSearch** (3–5 values per 2–3 key knobs) or even use **sane defaults** and stop.

Below is a streamlined, minimal-friction recipe you can drop into your codebase.

---

## 1) Minimal Workflow (Keep It Simple)

1. **Split correctly:** `GroupKFold(n_splits=5)` using `well_id`.
2. **Weights (optional):** Use per-sample thickness `dz_m` as `sample_weight`.
3. **Pick one search method:**

   * **Default:** `RandomizedSearchCV` (fast, broad).
   * **If time < 10 min:** tiny `GridSearchCV` over 2–3 hyperparameters.
4. **Tune only the high-impact knobs** for each model.
5. **Score with weighted RMSE**; also log MAE and R².
6. **Post-tuning:** sanity plots + (optional) SHAP on a small subset.

---

## 2) One-File Quickstart (RandomizedSearchCV only)

```python
# --- Setup
import numpy as np, pandas as pd
from sklearn.model_selection import GroupKFold, RandomizedSearchCV
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.metrics import make_scorer, mean_absolute_error, mean_squared_error, r2_score

# Data assumptions
# df columns: well_id, DEPTH_MD, target (e.g., "DT"), predictors (e.g., GR, RHOB, NPHI, RT, PE)
target = "DT"
features = ["GR", "RHOB", "NPHI", "RT", "PE"]
df = df.sort_values(["well_id", "DEPTH_MD"]).reset_index(drop=True)
df["dz_m"] = df.groupby("well_id")["DEPTH_MD"].diff().fillna(method="bfill").replace([0, np.nan], 1.0)

X, y = df[features], df[target]
groups = df["well_id"]
w = df["dz_m"]

# Preprocess (simple & uniform)
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer

preprocess = ColumnTransformer([
    ("num", Pipeline([("imputer", SimpleImputer(strategy="median"))]), features)
], remainder="drop")

# Metrics (weighted)
def rmse(y_true, y_pred, sample_weight=None):
    return mean_squared_error(y_true, y_pred, sample_weight=sample_weight, squared=False)
rmse_scorer = make_scorer(rmse, greater_is_better=False)
mae_scorer  = make_scorer(mean_absolute_error, greater_is_better=False)

cv = GroupKFold(n_splits=5)
```

### 2.1 Pick ONE model and tune it (examples)

**LightGBM (good default):**

```python
from lightgbm import LGBMRegressor
from scipy.stats import randint, uniform, loguniform

pipe_lgbm = Pipeline([("prep", preprocess),
                      ("model", LGBMRegressor(objective="regression", n_jobs=-1, random_state=42))])

param_dist_lgbm = {
    "model__n_estimators": randint(400, 1100),
    "model__learning_rate": loguniform(1e-3, 1e-1),
    "model__num_leaves": randint(31, 127),
    "model__max_depth": randint(4, 10),
    "model__min_child_samples": randint(10, 60),
    "model__subsample": uniform(0.7, 0.3),           # 0.7–1.0
    "model__colsample_bytree": uniform(0.6, 0.4),    # 0.6–1.0
}

search = RandomizedSearchCV(
    estimator=pipe_lgbm,
    param_distributions=param_dist_lgbm,
    n_iter=40,                 # 30–60 is a good sweet spot
    cv=cv,
    scoring=rmse_scorer,
    n_jobs=-1,
    random_state=42,
    verbose=1,
    return_train_score=True
)

search.fit(X, y, groups=groups, model__sample_weight=w)
best_lgbm = search.best_estimator_
```

**XGBoost (alternative):**

```python
from xgboost import XGBRegressor
from scipy.stats import randint, uniform, loguniform

pipe_xgb = Pipeline([("prep", preprocess),
                     ("model", XGBRegressor(objective="reg:squarederror",
                                            tree_method="hist", n_jobs=-1, random_state=42))])

param_dist_xgb = {
    "model__n_estimators": randint(400, 1100),
    "model__learning_rate": loguniform(1e-3, 1e-1),
    "model__max_depth": randint(3, 8),
    "model__min_child_weight": randint(1, 6),
    "model__subsample": uniform(0.7, 0.3),
    "model__colsample_bytree": uniform(0.6, 0.4),
    "model__reg_alpha": loguniform(1e-4, 1),
    "model__reg_lambda": loguniform(1e-1, 10),
}
# Then run the same RandomizedSearchCV(...) as above.
```

**CatBoost (simple, strong baseline):**

```python
from catboost import CatBoostRegressor

pipe_cat = Pipeline([("prep", preprocess),
                     ("model", CatBoostRegressor(loss_function="RMSE", random_seed=42, verbose=False))])

# Minimal grid disguised as random by sampling from a small set:
from numpy.random import default_rng
rng = default_rng(42)
param_dist_cat = {
    "model__iterations": rng.choice([800, 1200, 1600], size=40),
    "model__depth":      rng.choice([6, 8, 10], size=40),
    "model__learning_rate": rng.choice([0.01, 0.05, 0.1], size=40),
    "model__l2_leaf_reg": rng.choice([1, 3, 5, 10], size=40),
    "model__subsample":  rng.choice([0.7, 0.85, 1.0], size=40),
    "model__rsm":        rng.choice([0.6, 0.8, 1.0], size=40),
}
# Same RandomizedSearchCV(...) pattern.
```

**If you must be even simpler (tiny GridSearchCV):**

* Tune **only 2–3 knobs**: `n_estimators`, `max_depth/num_leaves`, `min_child_samples` (or `min_child_weight`).
* Use **3 values each** (e.g., `n_estimators: [500, 800, 1100]`, `max_depth: [4, 6, 8]`, `min_child_samples: [10, 30, 60]`).

---

## 3) What to Monitor (just the essentials)

* **Primary metric:** weighted **RMSE** (`dz_m`).
* **Also log:** MAE, R².
* **Per-well scores:** to reflect blind-well deployment.
* **Fold variance:** big variance ⇒ sensitive to which wells are held out.

---

## 4) Post-Tuning (Minimal & Useful)

1. **Residual checks:** predicted vs actual, residual vs depth per well (look for drifts/spikes).
2. **Feature importance / SHAP (optional):** run **on a small subsample** per well to confirm drivers are geologically sensible.

   * If time is tight, a **bar importance** from the final model is often enough.
3. **Save artifacts:** best estimator (`joblib.dump`), feature list, units, fold/well membership.

*Example (SHAP on a tiny subset):*

```python
# Optional, in a notebook environment
import shap
final_model = best_lgbm  # or best_xgb / best_cat
bg_idx = (df.groupby("well_id", group_keys=False)
            .apply(lambda g: g.sample(min(len(g), 200), random_state=42))).index
X_bg = X.loc[bg_idx]
explainer = shap.Explainer(final_model.named_steps["model"])
shap_vals = explainer(final_model.named_steps["prep"].transform(X_bg))
# shap.plots.bar(shap_vals, max_display=10)
```

---

## 5) Tiny, Model-Specific “High-Impact” Knobs

| Model        | Tune these first                                                                                  | Typical small ranges                                                         |
| ------------ | ------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------- |
| **LightGBM** | `num_leaves`, `max_depth`, `min_child_samples`, `learning_rate`, `n_estimators`                   | leaves 31–127; depth 4–10; min\_child 10–60; lr 0.01–0.1; trees 400–1100     |
| **XGBoost**  | `max_depth`, `min_child_weight`, `learning_rate`, `n_estimators`, `subsample`, `colsample_bytree` | depth 3–8; mcw 1–5; lr 0.01–0.1; trees 400–1100; subsample/colsample 0.6–1.0 |
| **CatBoost** | `depth`, `iterations`, `learning_rate`, `l2_leaf_reg`                                             | depth 6–10; iters 800–1600; lr 0.01–0.1; l2 1–10                             |
| **RF/ET**    | `n_estimators`, `max_depth`, `min_samples_leaf`                                                   | trees 400–1000; depth 10–40/None; leaf 2–10                                  |

---

## 6) Minimal Checklist

* [ ] **GroupKFold by `well_id`**, not row-split.
* [ ] **Weighted RMSE** with `dz_m`.
* [ ] **One search only** (prefer `RandomizedSearchCV`, \~40 trials).
* [ ] Tune **few** high-impact knobs; keep ranges narrow.
* [ ] Review **per-well** metrics and a couple of residual plots.
* [ ] (Optional) quick **SHAP/importance** sanity check.
* [ ] Save model + config (features, units, folds).

---

### Bottom line

For computational efficiency and simplicity: **use just `RandomizedSearchCV`** with **\~40 trials**, **GroupKFold by well**, and **weighted RMSE**. Only if the best region is unclear and you still have time, do a **tiny local GridSearch**. Otherwise, stop—deploy, and iterate later only if needed.
