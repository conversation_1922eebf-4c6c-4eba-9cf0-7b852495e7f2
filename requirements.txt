# Core data science libraries
pandas>=1.3.0
numpy>=1.21.0

# Machine learning libraries
scikit-learn>=1.0.0
xgboost>=1.5.0
lightgbm>=3.2.0
catboost>=1.0.0

# Scientific computing (required by utils/metrics.py)
scipy>=1.7.0

# Well log data handling
lasio>=0.30

# Visualization
matplotlib>=3.5.0

# GUI for file dialogs (tkinter is usually included with Python)
# tkinter is part of standard library, no need to install
