# ML Model Integration: KNN, Decision Tree, Extra Trees, Random Forest

This document reviews the current workflow, analyzes the codebase architecture, and proposes minimal, backward-compatible improvements to add and integrate four additional models:
- K-Nearest Neighbors (KNN)
- Decision Tree
- Extra Trees
- Random Forest

It focuses on practical code-aligned changes that enable:
- Configuration-driven model selection (optional, non-breaking)
- Pluggable model entries aligned with the current registry
- Parameterizable but simple training (holdout only; no cross-validation)
- Flexible data preprocessing that preserves missing values (no ffill/bfill; no imputation)
- Modular evaluation metrics with a configurable composite score (optional)

Note: This is documentation only. No code has been modified yet.

## NaN Preservation Policy (No Handling Before Step 5)

- Preserve all NaN values exactly as-is throughout Steps 1–4; do not apply forward/back-fill, row dropping, statistical imputation, or any cleaning.
- Do not implement any NaN handling at this stage.
- Rationale: "Step 5: Imputation Mode" will perform missing value imputation later. Earlier steps must pass NaNs through unchanged.
- Implication: Models that cannot natively accept NaNs should be deferred/disabled until Step 5 (or until an explicit imputation policy is enabled). Do not workaround by dropping rows or imputing.



## 1) Review of the Current Process

Key files and flow today:
- main.py
  - Orchestrates the workflow and interactive model selection.
  - Builds per-model hyperparameters from MODEL_REGISTRY via configure_hyperparameters().
  - Instantiates and runs models: models_to_run = {display_name: model_class(**hyperparams)}
  - Calls impute_logs() and then generates reports/plots.

- ml_core.py
  - MODEL_REGISTRY: contains model metadata with keys: name, model_class, hyperparameters, fixed_params, gpu_check.
  - evaluate_model_comprehensive(): computes MAE, RMSE, R2 and a composite score (weights fixed at 0.5/0.3/0.2).
  - impute_logs():
    - Prepares features: feat_set = feature_cols + ['MD']
    - Selects training data (mixed or separated mode by wells).
    - CURRENTLY fills feature NaNs with forward-fill and back-fill:
      - Training: X = train[feat_set].ffill().bfill()
      - Prediction: X_pred = res.loc[pred_mask, feat_set].ffill().bfill()
    - Splits holdout train/val with train_test_split(..., test_size=0.25, random_state=42)
    - Trains each model, applying early stopping when supported (XGB/LGBM/CatBoost), evaluates via evaluate_model_comprehensive, picks best by lowest composite score, and predicts on X_pred.

- config_handler.py
  - Interactive selection helpers and default hyperparameters from MODEL_REGISTRY.

- data_handler.py
  - LAS loading, basic cleaning, optional enhanced preprocessing for deep learning (not used in current tree-based flow).

- reporting.py
  - QC report and several plot/report utilities.

Observations:
- The system is registry-driven; adding a model is primarily extending MODEL_REGISTRY and letting the rest of the pipeline operate unchanged.
- Preprocessing currently uses ffill/bfill, which is a form of imputation. The requested constraint is to preserve missing values as-is (no imputation). Some models (e.g., KNN/DecisionTree/RandomForest/ExtraTrees in scikit-learn) cannot accept NaNs; this must be handled conservatively without imputing values.


## 2) Codebase Architecture Analysis

- Entry point: main.py
  - Coordinates I/O, user prompts, model selection, calls impute_logs(), then reporting/plots.

- Core ML: ml_core.py
  - Central registry (MODEL_REGISTRY) and end-to-end train/eval/predict in impute_logs().
  - Common metrics with a composite score.

- Configuration/UI: config_handler.py
  - Interactive selection and hyperparameter defaults sourced from MODEL_REGISTRY.

- Data handling: data_handler.py
  - Data loading, cleaning rules, LAS I/O, optional normalization helpers.

- Reporting: reporting.py
  - Visualization and textual performance summaries.

Integration pattern:
- Registry → Hyperparams → Instantiate → Train/Eval → Predict → Report.
- Minimal and centralized. This pattern should be preserved.


## 3) Proposed Targeted Improvements (minimal, backward-compatible)

A. Configuration-driven selection (optional)
- Add an optional configuration layer (JSON or YAML if available) to preselect models and override hyperparameters. If no config is provided, retain the current interactive prompts and defaults.

B. Pluggable model entries (keep current pattern)
- Continue to extend MODEL_REGISTRY in ml_core.py for simplicity. Optionally, add a lightweight plugin mechanism later without breaking existing behavior.

C. Parameterizable training pipeline (keep simple)
- Keep holdout split only (no cross-validation), but allow test_size and random_state to be set via config.
- Retain early stopping for models that support it (XGB/LGBM/CatBoost) as today.

D. Flexible data preprocessing without imputation
- Remove ffill/bfill in impute_logs(). Preserve NaNs as-is.
- Do not drop rows, forward/back-fill, or otherwise handle NaNs at this stage.
- If a selected model cannot accept NaNs, defer its training/prediction until Step 5 or until an explicit imputation policy is enabled.
- Models that accept NaNs (e.g., XGB/LGBM/CatBoost) can pass NaNs through unchanged.

Note: Missing value imputation is exclusively handled in "Step 5: Imputation Mode" later in the process. All earlier steps must leave NaNs unchanged. See Section 4.5 for details.

E. Modular evaluation metrics (optional)
- Keep existing MAE, RMSE, R2.
- Make composite score weights configurable (default to current 0.5, 0.3, 0.2).

All items above can be implemented with small, localized edits, preserving current UX and code flow.


## 4) Implementation Details (concrete examples)

4.1 Add the four models to MODEL_REGISTRY (ml_core.py)

Add imports near the top of ml_core.py:

```python
from sklearn.neighbors import KNeighborsRegressor
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor
```

Then extend MODEL_REGISTRY with entries aligned to the current schema (name, model_class, hyperparameters, fixed_params). We also add an optional supports_nans flag to each entry for preprocessing logic. This additional key is backward-compatible because existing code ignores unknown keys.

```python
MODEL_REGISTRY.update({
    'knn': {
        'name': 'KNN',
        'model_class': KNeighborsRegressor,
        'hyperparameters': {
            'n_neighbors': {'type': int, 'default': 5, 'min': 1, 'max': 100, 'prompt': 'Number of neighbors'},
            'weights': {'type': str, 'default': 'distance', 'options': ['uniform', 'distance'], 'prompt': 'Weighting scheme'},
            'p': {'type': int, 'default': 2, 'min': 1, 'max': 2, 'prompt': 'Minkowski power (1=Manhattan, 2=Euclidean)'}
        },
        'fixed_params': {},
        'supports_nans': False
    },
    'decision_tree': {
        'name': 'Decision Tree',
        'model_class': DecisionTreeRegressor,
        'hyperparameters': {
            'max_depth': {'type': int, 'default': None, 'min': 1, 'max': 100, 'prompt': 'Max tree depth (None for full)'},
            'min_samples_split': {'type': int, 'default': 2, 'min': 2, 'max': 50, 'prompt': 'Min samples to split'},
            'min_samples_leaf': {'type': int, 'default': 1, 'min': 1, 'max': 50, 'prompt': 'Min samples at leaf'},
        },
        'fixed_params': {'random_state': 42},
        'supports_nans': False
    },
    'extra_trees': {
        'name': 'Extra Trees',
        'model_class': ExtraTreesRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 200, 'min': 50, 'max': 2000, 'prompt': 'Number of trees'},
            'max_depth': {'type': int, 'default': None, 'min': 1, 'max': 100, 'prompt': 'Max tree depth (None for full)'},
            'min_samples_split': {'type': int, 'default': 2, 'min': 2, 'max': 50, 'prompt': 'Min samples to split'},
            'min_samples_leaf': {'type': int, 'default': 1, 'min': 1, 'max': 50, 'prompt': 'Min samples at leaf'},
        },
        'fixed_params': {'random_state': 42, 'n_jobs': -1},
        'supports_nans': False
    },
    'random_forest': {
        'name': 'Random Forest',
        'model_class': RandomForestRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': 'Number of trees'},
            'max_depth': {'type': int, 'default': None, 'min': 1, 'max': 100, 'prompt': 'Max tree depth (None for full)'},
            'min_samples_split': {'type': int, 'default': 2, 'min': 2, 'max': 50, 'prompt': 'Min samples to split'},
            'min_samples_leaf': {'type': int, 'default': 1, 'min': 1, 'max': 50, 'prompt': 'Min samples at leaf'},
        },
        'fixed_params': {'random_state': 42, 'n_jobs': -1},
        'supports_nans': False
    },
})
```

Notes:
- We align with the existing hyperparameters metadata style to keep configure_hyperparameters() working.
- No gpu_check is needed for these scikit-learn models.

4.2 NaN-preserving preprocessing in impute_logs() (ml_core.py)

Goal: Remove ffill/bfill and preserve missing values as-is. For models that do not support NaNs, restrict training, validation, and prediction to rows with complete features.

Minimal, surgical changes:
- Replace X = train[feat_set].ffill().bfill() with X = train[feat_set]
- Replace X_pred = res.loc[pred_mask, feat_set].ffill().bfill() with X_pred = res.loc[pred_mask, feat_set]
- Inside the training loop, derive per-model valid masks if the model does not support NaNs.

Illustrative unified diff (for reference; exact line numbers may differ):

```diff
--- a/ml_core.py
+++ b/ml_core.py
@@
-    # Use forward-fill and back-fill to handle missing data in the feature set.
-    X = train[feat_set].ffill().bfill()
+    # Preserve missing values as-is (no imputation)
+    X = train[feat_set]
     y = train[target_col]
     X_tr, X_val, y_tr, y_val = train_test_split(X, y, test_size=0.25, random_state=42)
@@
-    # Use forward-fill and back-fill to handle missing data in the prediction set.
-    X_pred = res.loc[pred_mask, feat_set].ffill().bfill()
+    # Preserve missing values in the prediction set (no imputation)
+    X_pred = res.loc[pred_mask, feat_set]
```

At this stage, do not add any additional NaN handling or row filtering. The only change is to remove forward/back-fill so NaNs are preserved as-is.

If a selected model cannot accept NaNs, skip or defer its training/prediction until Step 5 establishes the imputation policy.

This approach:
- Preserves NaNs (no imputation) per requirements.
- Trains and predicts only where features are complete for models that require it.
- Keeps the rest of the pipeline unchanged.

4.3 Optional: Configurable composite score weights

To keep backward compatibility, default to current weights inside evaluate_model_comprehensive(). If a config is present, use it.

Example change in ml_core.py:

```python
def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val, weights=None):
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)

    w = weights or {'mae': 0.5, 'rmse': 0.3, 'r2_penalty': 0.2}
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
    composite = (mae * w['mae']) + (rmse * w['rmse']) + (r2_penalty * w['r2_penalty'])
    return {'mae': mae, 'rmse': rmse, 'r2': r2, 'composite_score': composite}
```

Call sites can continue to omit weights to keep current behavior.

4.4 Optional: Simple configuration helpers

Add JSON/YAML config readers (non-breaking) in config_handler.py to allow preselecting models and overriding hyperparameters. If not provided, keep the interactive selection.

```python
# config_handler.py additions (optional)
import json, os
try:
    import yaml
except Exception:
    yaml = None

def load_config(path: str | None) -> dict:
    if not path or not os.path.exists(path):
        return {}
    with open(path, 'r', encoding='utf-8') as f:
        if path.lower().endswith(('.yml', '.yaml')) and yaml:
            return yaml.safe_load(f) or {}
        return json.load(f)

def apply_config_overrides(hparams: dict, cfg: dict) -> dict:
    overrides = (cfg.get('models') or {}).get('hyperparameters', {})
    for key, vals in overrides.items():
        if key in hparams:
            hparams[key].update(vals)
    return hparams
```

Example config snippet (YAML):

```yaml
models:
  enabled: [random_forest, knn]
  hyperparameters:
    random_forest:
      n_estimators: 400
      max_depth: 20
      min_samples_leaf: 2
    knn:
      n_neighbors: 7
      weights: distance
training:
  test_size: 0.2
  random_state: 123
metrics:
  composite_weights:
    mae: 0.5
    rmse: 0.3
    r2_penalty: 0.2
preprocessing:
  strategy: passthrough   # recommended (no imputation)
```

You can wire this into main.py to preselect models if config.models.enabled is present; otherwise, continue with console selection.


4.5 Step 5: Prediction Mode and Target Imputation

Imputation in this codebase is an output-time operation controlled by Step 5 (prediction mode). This is separate from feature preprocessing. The new models integrate without changing these semantics.

- prediction_mode == 2 (Full): the model’s predictions fully populate the imputed target column.
- prediction_mode == 1 (Fill-missing): only missing target values are filled with predictions; existing original target values are preserved.

The relevant mapping in ml_core.py looks like this:

```python
imp_col = f"{target_col}_imputed"
pred_col = f"{target_col}_pred"

res[pred_col] = full_pred
if prediction_mode == 2:  # Full
    res[imp_col] = full_pred
else:  # Fill-missing
    res[imp_col] = res[target_col].fillna(full_pred)
```

Notes for the new models:
- For models that do not accept NaNs in features (KNN, Decision Tree, Random Forest, Extra Trees), only rows with complete feature vectors are predicted; other rows remain NaN in full_pred. Step 5 then applies the same mapping—so in Fill-missing mode, rows with no prediction remain as originally missing; in Full mode they also remain NaN because the model could not predict there.
- Error column computation and reporting remain unchanged.


## 5) Compatibility Requirements

- No config provided: behavior remains unchanged (interactive selection, default hyperparameters from MODEL_REGISTRY, same reporting).
- Adding new registry entries does not break existing code; unknown keys like supports_nans are ignored elsewhere.
- Training remains simple holdout (no cross-validation introduced).
- Early stopping behavior for boosting models is unchanged.
- Legacy forward/back-fill should not be used at this stage; keep NaNs unchanged until Step 5 handles imputation.


## 6) Minimal Change Approach (surgical diffs)

Summary of minimal edits:
1) Add four models to MODEL_REGISTRY (imports + registry entries).
2) Remove .ffill().bfill() when constructing X and X_pred in impute_logs().
3) Add name_nan_support mapping and apply per-model filtering for models that do not accept NaNs during train/eval and predict.
4) Optionally make composite score weights configurable (defaults preserved).
5) Optionally add config helpers (no behavior change without a config file).

No changes are required in reporting.py or data_handler.py for these additions.


## 7) Data Preprocessing Constraints (NaN preservation)

- Preserve missing values exactly as-is; do not forward/back-fill, impute, drop rows, or perform cleaning in Steps 1–4.
- Models that natively accept NaNs (XGBoost, LightGBM, CatBoost) can be used without change.
- Models that do not accept NaNs (e.g., KNN, Decision Tree, Random Forest, Extra Trees) should be deferred/disabled until Step 5 defines the imputation policy.

This keeps the pipeline simple and defers imputation to Step 5.


## 8) Training Approach (simple)

- Continue using train_test_split with test_size=0.25 and random_state=42 by default.
- Make test_size/random_state configurable if a config is supplied.
- Do not introduce cross-validation or hyperparameter search.
- Keep early stopping logic only for models that expose an early_stopping_rounds parameter (as currently implemented).


## 9) Step-by-step Integration Guide (KNN, Decision Tree, Extra Trees, Random Forest)

1) Extend MODEL_REGISTRY in ml_core.py
- Add the imports and MODEL_REGISTRY.update(...) block from Section 4.1.

2) Update impute_logs() preprocessing
- Replace ffill/bfill usage with direct selection of X = train[feat_set] and X_pred = res.loc[pred_mask, feat_set].
- Do not add any NaN handling or row filtering yet; if a chosen model cannot accept NaNs, defer it until Step 5.

3) Keep training simple
- No cross-validation; retain the existing holdout split.
- Early stopping remains only for boosting libraries.

4) Optional configuration
- If desired, add config helpers in config_handler.py and allow preselecting models and overriding hyperparameters.
- You may make composite score weights configurable but keep defaults if not provided.

5) Run and verify
- Use the existing interactive flow in main.py to select any of: knn, decision_tree, extra_trees, random_forest.
- Confirm metrics and plots are produced as usual. For the new models, predictions will be NaN where features are incomplete (by design, no imputation).


## 10) Appendix

A. JSON config example (no PyYAML required):

```json
{
  "models": {
    "enabled": ["random_forest", "knn"],
    "hyperparameters": {
      "random_forest": {"n_estimators": 400, "max_depth": 20, "min_samples_leaf": 2},
      "knn": {"n_neighbors": 7, "weights": "distance", "p": 2}
    }
  },
  "training": {"test_size": 0.2, "random_state": 123},
  "metrics": {"composite_weights": {"mae": 0.5, "rmse": 0.3, "r2_penalty": 0.2}},
  "preprocessing": {"strategy": "passthrough"}
}
```

B. Notes on performance
- KNN can be slow on large datasets; consider downsampling or feature selection if needed.
- Tree ensembles (RF/ET) are generally robust; ensure n_jobs=-1 is set for parallelism where supported.

C. Alignment with existing docs
- This plan complements 2_Step_Adding_ML_Model.md and keeps the same registry-first pattern.

---

Deliverable status: This file documents all proposed changes and exact code snippets. No code has been changed yet. Apply the minimal diffs in ml_core.py to adopt NaN-preserving preprocessing and register the four new models.
