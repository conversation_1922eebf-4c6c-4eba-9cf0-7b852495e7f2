# CV Option Removal Guide (Step 5)

This guide describes how to remove the CV (Cross-Validation) option from Step 5 of the main pipeline, renumber the remaining options, and validate the system after the change.

---

## 1) Identification: Where the CV option is implemented

- Step 5 UI entry point in the main workflow
  - File: `main.py`
  - Responsibility: Calls the prediction mode selector and prints the selected mode
  - Reference snippet:

    ```python
    # Step 5: Configure prediction mode
    print("\n⚙️ Step 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"✅ Prediction mode: {mode}")
    ```

- Prediction mode selection (where the CV option is listed)
  - File: `config_handler.py`
  - Function: `get_prediction_mode()`
  - BEFORE:

    ```python
    def get_prediction_mode():
        mode = console_select(['1','2','3'], "Prediction mode: 1 fill-missing, 2 CV, 3 full", default='1')
        return int(mode)
    ```

- Prediction mode usage (controls imputation behavior)
  - File: `ml_core.py`
  - Function: `impute_logs(...)`
  - The numeric mapping ties "Full" behavior to value `3`:

    ```python
    res[pred_col] = full_pred
    if prediction_mode == 3:
        res[imp_col] = full_pred
    else:
        res[imp_col] = res[target_col].fillna(full_pred)
    ```

- Documentation mentioning CV
  - File: `archives/second_stage/RESTRUCTURING_COMPLETE.md`
  - Mentions: "Prediction Mode: Fill-missing, Cross-validation, or Full prediction"

---

## 2) Removal Process: Safely remove the CV option

Follow these steps in order:

1. Update the prediction mode selector to remove CV and renumber options
   - File: `config_handler.py`
   - Function: `get_prediction_mode`
   - Change options from `['1','2','3']` to `['1','2']`
   - Update prompt text from "1 fill-missing, 2 CV, 3 full" to "1 fill-missing, 2 full"
   - Keep default as `'1'`

2. Update the imputation behavior mapping to reflect the new numbering
   - File: `ml_core.py`
   - Function: `impute_logs`
   - Change the check from `prediction_mode == 3` to `prediction_mode == 2` to map "Full" to option 2

3. Update any user-facing text, help, or prompts that reference "CV"
   - File: `archives/second_stage/RESTRUCTURING_COMPLETE.md`
   - Replace "Fill-missing, Cross-validation, or Full" with "Fill-missing or Full"

4. Search and remove/adjust any additional references to CV
   - Search terms: `CV`, `Cross-validation`, `Prediction mode:`
   - Update docs or code comments accordingly

---

## 3) Option Renumbering

- Previous mapping:
  - 1 = Fill-missing
  - 2 = CV
  - 3 = Full

- New mapping (after removal):
  - 1 = Fill-missing (unchanged)
  - 2 = Full (renumbered from 3)

Make sure all logic that previously checked for `3` (Full) now checks for `2`.

---

## 4) Code Changes (files and exact edits)

- `config_handler.py` — remove CV from selector and renumber
  - BEFORE:

    ```python
    def get_prediction_mode():
        mode = console_select(['1','2','3'], "Prediction mode: 1 fill-missing, 2 CV, 3 full", default='1')
        return int(mode)
    ```

  - AFTER:

    ```python
    def get_prediction_mode():
        mode = console_select(['1','2'], "Prediction mode: 1 fill-missing, 2 full", default='1')
        return int(mode)
    ```

- `ml_core.py` — remap Full from 3 to 2
  - BEFORE:

    ```python
    res[pred_col] = full_pred
    if prediction_mode == 3:
        res[imp_col] = full_pred
    else:
        res[imp_col] = res[target_col].fillna(full_pred)
    ```

  - AFTER:

    ```python
    res[pred_col] = full_pred
    if prediction_mode == 2:  # Full
        res[imp_col] = full_pred
    else:  # Fill-missing
        res[imp_col] = res[target_col].fillna(full_pred)
    ```

- `archives/second_stage/RESTRUCTURING_COMPLETE.md` — update documentation wording
  - BEFORE (example line):

    ```text
    - Prediction Mode: Fill-missing, Cross-validation, or Full prediction
    ```

  - AFTER:

    ```text
    - Prediction Mode: Fill-missing or Full prediction
    ```

Note: No other code paths directly use a special behavior for the old `2` (CV) value, so updates are confined to the selector and the Full-mode numeric check.

---

## 5) Testing: Verify the pipeline after removal

1. Interactive run-through
   - Run: `python main.py`
   - Step 5 should now show only two options in the prompt text: `1 fill-missing, 2 full`
   - Choose option 1 (Fill-missing) and complete the workflow to the end
   - Run again and choose option 2 (Full) and complete the workflow

2. Behavioral validation in output data
   - For Fill-missing (1):
     - The `<target>_imputed` column should preserve original non-NaN values and only fill NaNs with predictions
   - For Full (2):
     - The `<target>_imputed` column should be fully replaced by the model’s predicted curve (i.e., identical to `<target>_pred` on predicted intervals)

3. Console/UI checks
   - Ensure there is no mention of "CV" in any prompts or prints
   - Ensure the selected mode prints as `1` or `2` and aligns with the new mapping

4. Smoke tests across training/prediction strategies
   - Test both `mixed` and `separated` well configurations in Step 4 to ensure no regressions

---

## 6) Validation Checklist

Use this checklist before considering the change complete:

- [ ] `config_handler.py::get_prediction_mode` now offers only `1` and `2`, with prompt "1 fill-missing, 2 full"
- [ ] `ml_core.py::impute_logs` checks `prediction_mode == 2` for Full, otherwise Fill-missing
- [ ] `archives/second_stage/RESTRUCTURING_COMPLETE.md` updated to remove "Cross-validation"
- [ ] Global search shows no remaining references to `CV` or `Cross-validation` in prompts, help, or docs (except historical notes)
- [ ] Running the pipeline with option 1 produces expected Fill-missing behavior
- [ ] Running the pipeline with option 2 produces expected Full behavior
- [ ] No exceptions thrown while navigating Steps 1–9 (UI, error handling intact)

---

## Notes on Error Handling and UI

- The `console_select` helper continues to handle invalid inputs and defaults as before; no changes required
- Ensure any user-facing text, README sections, or training material that previously mentioned a CV option are revised to avoid user confusion

---

## Appendix: Quick Diffs

- `config_handler.py`

  ```diff
  - mode = console_select(['1','2','3'], "Prediction mode: 1 fill-missing, 2 CV, 3 full", default='1')
  + mode = console_select(['1','2'], "Prediction mode: 1 fill-missing, 2 full", default='1')
  ```

- `ml_core.py`

  ```diff
  - if prediction_mode == 3:
  + if prediction_mode == 2:
  ```